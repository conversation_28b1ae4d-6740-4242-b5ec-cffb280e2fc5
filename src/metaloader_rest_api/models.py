import json
import uuid
from datetime import datetime, timedelta, timezone
from enum import IntEnum
from functools import partial
from typing import Dict, List, Literal, Optional

from pydantic import BaseModel
from sqlalchemy import (
    BigInteger,
    Column,
    DateTime,
    ForeignKey,
    SmallInteger,
    Text,
    create_engine,
)
from sqlalchemy.dialects.postgresql import ARRA<PERSON>, JSONB, UUID
from sqlalchemy.orm import Session, declarative_base, relationship, sessionmaker

from metaloader_rest_api.helpers import JsonDict

Base = declarative_base()

METAMODEL_SERVICE_ID = 1  # service_cd=metamodel_loader_masterflow


class TransactionStatus(IntEnum):
    OPENED = 1  # service_transaction_status_cd=created
    COMMITTED = 4  # =commit
    ROLLEDBACK = 5  # =rollback
    IN_PROGRESS = 6  # =?
    ETL_ERROR = 7  # =?


NON_RESOLVED_STATUSES = (
    TransactionStatus.OPENED,
    TransactionStatus.IN_PROGRESS,
)


class TransactionMode(IntEnum):
    SNAPSHOT = 1
    INCREMENT = 2


class MasterFlowAction(IntEnum):
    ADD = 1
    MODIFY = 2
    RENAME = 3
    DELETE = 4


class ResourceAction(IntEnum):
    ADD = 1
    MODIFY = 2
    DELETE = 4


def now_at_utc() -> datetime:
    return datetime.now(timezone.utc)


class TransactionEntity(IntEnum):
    MASTER_FLOW = 1
    RESOURCE = 2


class BaseTransaction(Base):
    TIMEOUT_DEFAULT = timedelta(hours=3)
    __tablename__ = "service_transaction_status"
    __table_args__ = {"schema": "metamodel"}

    id: uuid.UUID = Column(
        "service_transaction_uid",
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
    )
    entity_type = Column(
        "entity_type",
        SmallInteger,
        nullable=False,
    )
    created_at: datetime = Column(
        "request_dttm",
        DateTime(timezone=True),
        nullable=False,
        default=now_at_utc,
    )
    status_changed_at: datetime = Column(
        "status_dttm",
        DateTime(timezone=True),
        nullable=False,
        default=now_at_utc,
    )
    resolved_at: Optional[datetime] = Column(
        "response_dttm",
        DateTime(timezone=True),
    )
    service_rk: Literal[1] = Column(
        "service_rk",
        SmallInteger,
        nullable=False,
        default=METAMODEL_SERVICE_ID,
    )
    status: int = Column(
        "service_transaction_status_rk",
        SmallInteger,
        nullable=False,
    )
    mode: Optional[int] = Column(
        "data_processing_mode_rk",
        SmallInteger,
    )
    timeout_sec: int = Column(
        "transaction_timeout_duration",
        BigInteger,
    )
    # FIX-ME: сделать отдельную модель
    release_metadata: JsonDict = Column(
        "transaction_request_json",
        JSONB,
    )
    resolution_details: Optional[JsonDict] = Column(
        "transaction_response_json",
        JSONB,
    )
    expected_master_flows: Optional[List[str]] = Column(
        "request_param_list",
        ARRAY(Text),
    )

    __mapper_args__ = {
        "polymorphic_identity": "base",
        "polymorphic_on": entity_type,
    }


class MasterFlowTransaction(BaseTransaction):
    __mapper_args__ = {"polymorphic_identity": TransactionEntity.MASTER_FLOW}

    master_flows = relationship(
        "MasterFlowTransactionItem",
        back_populates="transaction",
    )


class ResourceTransaction(BaseTransaction):
    __mapper_args__ = {"polymorphic_identity": TransactionEntity.RESOURCE}


class MasterFlowTransactionItem(Base):
    __tablename__ = "service_transaction_data"
    __table_args__ = {"schema": "metamodel"}

    id: int = Column(
        "service_transaction_data_rk",
        BigInteger,
        primary_key=True,
        autoincrement=True,
    )
    created_at: datetime = Column(
        "create_dttm",
        DateTime,
        nullable=False,
        default=now_at_utc,
    )
    name: str = Column(
        "name",
        Text,
        nullable=False,
    )
    old_name: Optional[str] = Column(
        "old_name",
        Text,
    )
    content_json: Optional[JsonDict] = Column(
        "content_json",
        JSONB,
    )
    action: int = Column(
        "data_action_type_rk",
        SmallInteger,
        nullable=False,
    )
    transaction_id: uuid.UUID = Column(
        "service_transaction_uid",
        UUID(as_uuid=True),
        ForeignKey("metamodel.service_transaction_status.service_transaction_uid"),
        nullable=False,
    )

    transaction = relationship(
        "MasterFlowTransaction",
        back_populates="master_flows",
    )


class TransactionResolutionDetails(BaseModel):
    """
    Инфо о результате выполнения транзакции типа MasterFlowTransaction
    TO-DO: если поймём, что для ResourceTransaction подходит та же схема, то переименовать поле
    `master_flow_errors` во что-то более общее (e.g. просто `errors`)
    """

    schema_version: Literal[1] = 1
    message: Optional[str] = None
    master_flow_errors: Optional[Dict[str, List[str]]] = None


class SessionFactory:
    def __init__(self, database_url: str):
        engine = create_engine(
            database_url,
            json_serializer=get_json_serializer(),
            pool_pre_ping=True,
        )
        self._make_session = sessionmaker(
            autocommit=False, autoflush=False, bind=engine
        )

    def __call__(self) -> Session:
        return self._make_session()


def get_json_serializer():
    return partial(json.dumps, default=str)
