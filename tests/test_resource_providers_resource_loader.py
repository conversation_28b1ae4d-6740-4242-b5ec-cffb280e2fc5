import json
from datetime import datetime
from pathlib import Path

from metaloader_rest_api.module_repository import ModuleRepository
from metaloader_rest_api.resource_provider_client import ResourceProviderClient
from metaloader_rest_api.resource_providers_resource_loader import (
    ResourceProvidersResourceLoader,
)
from metaloader_rest_api.resource_repository import (
    ResourceRepository,
    ResourceStageBatchRepository,
)
from metaloader_rest_api.version_repository import VersionRepository
from pytest import fixture
from sqlalchemy import text

CORE_MODULE_NAME = "core"


def test_load(
    uni_provider_url,
    ceh_provider_url,
    db_session,
    session_resource,
    table_id,
    expected_resource_cds,
    setup_metamodel,
):
    ResourceProvidersResourceLoader(
        uni_client=ResourceProviderClient(base_url=uni_provider_url.local),
        ceh_client=ResourceProviderClient(base_url=ceh_provider_url.local),
        version_repository=VersionRepository(db_session),
        module_repository=ModuleRepository(db_session),
        session_resource=session_resource,
        resource_stage_repository=ResourceStageBatchRepository(
            session=db_session,
            table_id=table_id,
        ),
        resource_repository=ResourceRepository(db_session),
    ).load(
        module=CORE_MODULE_NAME,
        version="1.2.3",
        effective_date=datetime(2024, 6, 15),
        limit=100,
    )

    # В тестовой базе может не быть unique_key на эти колонки для удобства, поэтому проверяем явно
    duplicate_resource_cds = (
        db_session.execute(
            text("""
            SELECT resource_cd, effective_from_dttm
              FROM metamodel.bridge_resource
             GROUP BY 1, 2
            HAVING count(*) > 1
        """)
        )
        .scalars()
        .fetchall()
    )
    assert not duplicate_resource_cds

    resources = (
        db_session.execute(
            text("SELECT resource_cd FROM metamodel.bridge_resource ORDER BY 1")
        )
        .scalars()
        .fetchall()
    )

    assert resources == expected_resource_cds


@fixture
def table_id() -> str:
    return 32 * "0"


@fixture
def setup_metamodel(db_session) -> str:
    sql = f"""
        TRUNCATE dict.dict_code_delivery
               , metamodel.bridge_version
               , metamodel.bridge_resource
               , metamodel.link_resource_source
               , metamodel.link_resource_table
               , metamodel.bridge_source
               , metamodel.bridge_table
        ;
        INSERT INTO dict.dict_code_delivery(code_delivery_rk
                                          , code_delivery_cd
                                          , code_delivery_name
                                          , is_ddl_flg
                                          , is_flow_flg
                                          , is_config_flg
                                          , deleted_flg)
        VALUES (1
              , '{CORE_MODULE_NAME}'
              , '{CORE_MODULE_NAME}'
              , FALSE
              , TRUE
              , FALSE
              , FALSE)
    """
    db_session.execute(text(sql))
    db_session.commit()


@fixture(scope="session")
def expected_resource_cds() -> list[str]:
    resources_dir = (
        Path(__file__).parent / "docker_" / "resource_provider" / "resources"
    )
    if not resources_dir.exists():
        raise RuntimeError(f"Resources directory not found: {resources_dir}")
    resource_cds = set()
    for json_file in resources_dir.glob("**/*.json"):
        with open(json_file, "r", encoding="utf-8") as f:
            data = json.load(f)
            resource_cds.add(data["resource_cd"])

    return sorted(resource_cds)
